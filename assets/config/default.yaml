# 认证服务管理器配置文件

# 认证服务配置
vm:
  # VMX文件路径（必须配置）
  vmx_path: ""
  # 服务名称
  name: "认证服务"
  # 客户机用户名（如果需要）
  username: ""
  # 客户机密码（加密存储）
  password: ""
  # 硬编码的主机ID（用于客户机验证）
  hardcoded_host_id: ""
  # 是否自动启动
  auto_start: false
  # 启动时是否验证主机ID
  validate_on_start: true

# 安全配置
security:
  # 是否加密密码
  encrypt_passwords: true
  # 是否验证主机ID
  validate_host_id: true
  # 验证成功后是否轮换ID（已简化，设为false）
  rotate_id_on_success: false
  # 验证超时时间（秒）
  validation_timeout: 30
  # 最大重试次数
  max_retries: 3

# 系统托盘配置
tray:
  # 是否显示通知
  show_notifications: true
  # 是否自动隐藏
  auto_hide: false
  # 状态更新间隔（秒）
  update_interval: 5
  # 图标主题
  icon_theme: "default"

# 日志配置
log:
  # 日志级别 (debug, info, warn, error)
  level: "info"
  # 日志文件路径
  file: "logs/auth_manager.log"
  # 最大文件大小（MB）
  max_size: 10
  # 最大备份文件数
  max_backups: 5
  # 最大保存天数
  max_age: 30
  # 是否压缩
  compress: true

# vmrun可执行文件路径
vmrun_path: "vmrun.exe"
