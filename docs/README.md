# 认证服务管理器

一个基于Go语言开发的VMware Workstation认证服务管理系统，提供主机ID验证和系统托盘管理功能。

## 功能特性

### 核心功能
- **主机ID生成与验证**：基于硬件信息生成唯一主机标识
- **认证服务管理**：启动、停止、重启认证服务
- **安全验证**：客户机自动验证主机ID，验证失败自动关闭
- **系统托盘界面**：友好的右键菜单操作

### 安全特性
- 硬件绑定的主机ID生成
- AES-256加密保护
- 客户机验证脚本保护
- 密码安全存储

## 系统要求

- Windows 10/11
- VMware Workstation Pro 15.0+
- Go 1.24+ (开发环境)

## 快速开始

### 1. 编译程序

```bash
# 克隆项目
git clone <repository-url>
cd license-server-wrapper

# 安装依赖
go mod tidy

# 编译
go build -o auth_manager.exe cmd/vmmanager/main.go
```

### 2. 配置文件

首次运行会在用户目录下创建配置文件：
```
%USERPROFILE%\.vmmanager\vmmanager.yaml
```

主要配置项：
```yaml
vm:
  vmx_path: "C:\\path\\to\\your\\vm.vmx"  # 必须配置
  name: "认证服务"
  hardcoded_host_id: "your_host_id_here"  # 需要设置

security:
  validate_host_id: true
  validation_timeout: 30

vmrun_path: "C:\\Program Files (x86)\\VMware\\VMware Workstation\\vmrun.exe"
```

### 3. 客户机设置

1. 将 `scripts/guest/validator.bat` 复制到客户机的 `C:\` 目录
2. 修改脚本中的 `EXPECTED_HOST_ID` 为实际的主机ID
3. 设置客户机自动运行该脚本（可通过启动项或计划任务）

### 4. 运行程序

```bash
# 直接运行
./auth_manager.exe

# 指定配置文件
./auth_manager.exe -config /path/to/config.yaml

# 调试模式
./auth_manager.exe -log debug
```

## 使用说明

### 系统托盘操作

右键点击系统托盘图标，可以进行以下操作：
- **启动认证服务**：启动VMware虚拟机
- **停止认证服务**：停止VMware虚拟机
- **重启认证服务**：重启VMware虚拟机
- **状态显示**：查看当前服务状态
- **配置**：打开配置（开发中）
- **查看日志**：查看日志文件（开发中）
- **退出**：退出程序

### 状态图标

- 🟢 绿色：认证服务运行中
- 🔴 红色：认证服务已停止
- 🟡 黄色：认证服务已暂停
- ❌ 红叉：认证服务出错
- ❓ 灰色：状态未知

### 主机ID验证流程

1. 程序启动时生成基于硬件信息的主机ID
2. 启动认证服务时，将加密的主机ID传递给客户机
3. 客户机运行验证脚本，比较接收到的ID与硬编码的ID
4. 验证成功：客户机继续运行
5. 验证失败：客户机自动关闭

## 配置详解

### 认证服务配置 (vm)
- `vmx_path`: VMX文件的完整路径
- `name`: 服务显示名称
- `hardcoded_host_id`: 客户机验证用的硬编码ID
- `validate_on_start`: 是否在启动时进行验证

### 安全配置 (security)
- `validate_host_id`: 是否启用主机ID验证
- `validation_timeout`: 验证超时时间（秒）
- `max_retries`: 最大重试次数

### 托盘配置 (tray)
- `show_notifications`: 是否显示系统通知
- `update_interval`: 状态更新间隔（秒）

### 日志配置 (log)
- `level`: 日志级别 (debug/info/warn/error)
- `file`: 日志文件路径
- `max_size`: 单个日志文件最大大小（MB）

## 故障排除

### 常见问题

1. **认证服务启动失败**
   - 检查VMX文件路径是否正确
   - 确认VMware Workstation已安装
   - 检查vmrun.exe路径配置

2. **主机ID验证失败**
   - 确认客户机中的硬编码ID与主机生成的ID匹配
   - 检查VMware Tools是否正常安装
   - 查看客户机日志文件 `C:\auth_validator.log`

3. **系统托盘不显示**
   - 检查Windows系统托盘设置
   - 确认程序有足够权限

### 日志查看

程序日志默认保存在：
```
logs/auth_manager.log
```

客户机验证日志保存在：
```
C:\auth_validator.log
```

## 开发说明

### 项目结构
```
license-server-wrapper/
├── cmd/vmmanager/          # 主程序入口
├── internal/               # 内部包
│   ├── config/            # 配置管理
│   ├── hostid/            # 主机ID生成
│   ├── vmware/            # VMware集成
│   ├── security/          # 安全功能
│   ├── tray/              # 系统托盘
│   └── logger/            # 日志系统
├── pkg/types/             # 类型定义
├── scripts/guest/         # 客户机脚本
└── assets/config/         # 配置文件模板
```

### 主要依赖
- `github.com/getlantern/systray`: 系统托盘
- `github.com/shirou/gopsutil/v4`: 系统信息
- `golang.org/x/crypto`: 加密功能
- `github.com/sirupsen/logrus`: 日志
- `gopkg.in/yaml.v3`: 配置解析

## 许可证

[许可证信息]

## 贡献

欢迎提交Issue和Pull Request。

## 联系方式

[联系信息]
