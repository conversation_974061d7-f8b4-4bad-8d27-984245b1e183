@echo off
setlocal enabledelayedexpansion

REM ========================================
REM 认证服务主机ID验证脚本 (Windows版本)
REM ========================================

echo [%date% %time%] 开始主机ID验证...

REM 设置日志文件
set LOG_FILE=C:\auth_validator.log
echo [%date% %time%] 主机ID验证开始 >> "%LOG_FILE%"

REM 检查VMware Tools是否可用
where vmware-toolbox-cmd.exe >nul 2>&1
if %errorlevel% neq 0 (
    echo [%date% %time%] 错误: VMware Tools未安装或不可用 >> "%LOG_FILE%"
    echo 错误: VMware Tools未安装或不可用
    goto :validation_failed
)

REM 获取主机传递的加密ID
echo [%date% %time%] 获取主机ID... >> "%LOG_FILE%"
for /f "tokens=*" %%i in ('vmware-toolbox-cmd.exe guestvar get host_id 2^>nul') do set HOST_ID=%%i

if "!HOST_ID!"=="" (
    echo [%date% %time%] 错误: 未能获取主机ID >> "%LOG_FILE%"
    echo 错误: 未能获取主机ID
    goto :validation_failed
)

echo [%date% %time%] 获取到主机ID >> "%LOG_FILE%"

REM 硬编码的预期主机ID（部署时需要替换为实际值）
set EXPECTED_HOST_ID=HARDCODED_HOST_ID_PLACEHOLDER

REM 验证主机ID
echo [%date% %time%] 验证主机ID... >> "%LOG_FILE%"

if "!HOST_ID!"=="!EXPECTED_HOST_ID!" (
    echo [%date% %time%] 主机ID验证成功 >> "%LOG_FILE%"
    echo 主机ID验证成功
    goto :validation_success
) else (
    echo [%date% %time%] 主机ID验证失败 >> "%LOG_FILE%"
    echo 主机ID验证失败
    goto :validation_failed
)

:validation_success
echo [%date% %time%] 验证成功，设置成功标志 >> "%LOG_FILE%"

REM 设置验证成功标志
vmware-toolbox-cmd.exe guestvar set validation_result success
if %errorlevel% neq 0 (
    echo [%date% %time%] 警告: 无法设置验证结果变量 >> "%LOG_FILE%"
)

echo [%date% %time%] 验证完成，认证服务继续运行 >> "%LOG_FILE%"
echo 验证完成，认证服务继续运行
exit /b 0

:validation_failed
echo [%date% %time%] 验证失败，准备关闭系统 >> "%LOG_FILE%"

REM 设置验证失败标志
vmware-toolbox-cmd.exe guestvar set validation_result failed
if %errorlevel% neq 0 (
    echo [%date% %time%] 警告: 无法设置验证结果变量 >> "%LOG_FILE%"
)

REM 记录失败原因
echo [%date% %time%] 主机ID验证失败，系统将在5秒后关闭 >> "%LOG_FILE%"
echo 主机ID验证失败，系统将在5秒后关闭

REM 显示倒计时
for /l %%i in (5,-1,1) do (
    echo 系统将在 %%i 秒后关闭...
    timeout /t 1 /nobreak >nul
)

REM 强制关闭系统
echo [%date% %time%] 执行系统关闭 >> "%LOG_FILE%"
shutdown /s /f /t 0

exit /b 1
