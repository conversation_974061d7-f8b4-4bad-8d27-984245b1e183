@echo off
setlocal enabledelayedexpansion

REM ========================================
REM 认证服务管理器安装脚本
REM ========================================

echo 认证服务管理器安装程序
echo ========================

REM 检查管理员权限
net session >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 需要管理员权限运行此安装程序
    echo 请右键点击此文件，选择"以管理员身份运行"
    pause
    exit /b 1
)

echo 正在安装认证服务管理器...

REM 设置安装目录
set INSTALL_DIR=%ProgramFiles%\AuthManager
set CONFIG_DIR=%USERPROFILE%\.vmmanager

echo 安装目录: %INSTALL_DIR%
echo 配置目录: %CONFIG_DIR%

REM 创建安装目录
if not exist "%INSTALL_DIR%" (
    mkdir "%INSTALL_DIR%"
    if %errorlevel% neq 0 (
        echo 错误: 无法创建安装目录
        pause
        exit /b 1
    )
)

REM 创建配置目录
if not exist "%CONFIG_DIR%" (
    mkdir "%CONFIG_DIR%"
    if %errorlevel% neq 0 (
        echo 错误: 无法创建配置目录
        pause
        exit /b 1
    )
)

REM 复制程序文件
echo 正在复制程序文件...
if exist "auth_manager.exe" (
    copy "auth_manager.exe" "%INSTALL_DIR%\"
    if %errorlevel% neq 0 (
        echo 错误: 无法复制主程序文件
        pause
        exit /b 1
    )
) else (
    echo 错误: 找不到 auth_manager.exe 文件
    echo 请确保在包含程序文件的目录中运行此安装脚本
    pause
    exit /b 1
)

REM 复制默认配置文件
echo 正在复制配置文件...
if exist "assets\config\default.yaml" (
    copy "assets\config\default.yaml" "%CONFIG_DIR%\vmmanager.yaml"
    if %errorlevel% neq 0 (
        echo 警告: 无法复制配置文件，将在首次运行时创建
    )
)

REM 创建桌面快捷方式
echo 正在创建桌面快捷方式...
set DESKTOP=%USERPROFILE%\Desktop
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%DESKTOP%\认证服务管理器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\auth_manager.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = '认证服务管理器'; $Shortcut.Save()"

REM 创建开始菜单快捷方式
echo 正在创建开始菜单快捷方式...
set START_MENU=%ProgramData%\Microsoft\Windows\Start Menu\Programs
if not exist "%START_MENU%\认证服务管理器" mkdir "%START_MENU%\认证服务管理器"
powershell -Command "$WshShell = New-Object -comObject WScript.Shell; $Shortcut = $WshShell.CreateShortcut('%START_MENU%\认证服务管理器\认证服务管理器.lnk'); $Shortcut.TargetPath = '%INSTALL_DIR%\auth_manager.exe'; $Shortcut.WorkingDirectory = '%INSTALL_DIR%'; $Shortcut.Description = '认证服务管理器'; $Shortcut.Save()"

REM 添加到系统PATH（可选）
echo 正在添加到系统PATH...
for /f "tokens=2*" %%a in ('reg query "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH 2^>nul') do set SYSTEM_PATH=%%b
echo %SYSTEM_PATH% | find /i "%INSTALL_DIR%" >nul
if %errorlevel% neq 0 (
    reg add "HKLM\SYSTEM\CurrentControlSet\Control\Session Manager\Environment" /v PATH /t REG_EXPAND_SZ /d "%SYSTEM_PATH%;%INSTALL_DIR%" /f >nul 2>&1
    if %errorlevel% equ 0 (
        echo 已添加到系统PATH
    ) else (
        echo 警告: 无法添加到系统PATH
    )
)

REM 创建卸载脚本
echo 正在创建卸载脚本...
(
echo @echo off
echo echo 正在卸载认证服务管理器...
echo taskkill /f /im auth_manager.exe ^>nul 2^>^&1
echo timeout /t 2 /nobreak ^>nul
echo if exist "%INSTALL_DIR%" rmdir /s /q "%INSTALL_DIR%"
echo if exist "%DESKTOP%\认证服务管理器.lnk" del "%DESKTOP%\认证服务管理器.lnk"
echo if exist "%START_MENU%\认证服务管理器" rmdir /s /q "%START_MENU%\认证服务管理器"
echo echo 卸载完成
echo pause
) > "%INSTALL_DIR%\uninstall.bat"

echo.
echo ========================
echo 安装完成！
echo ========================
echo.
echo 安装位置: %INSTALL_DIR%
echo 配置文件: %CONFIG_DIR%\vmmanager.yaml
echo.
echo 使用说明:
echo 1. 编辑配置文件，设置VMX文件路径
echo 2. 设置硬编码的主机ID
echo 3. 运行程序开始使用
echo.
echo 可以通过以下方式启动程序:
echo - 双击桌面快捷方式
echo - 从开始菜单启动
echo - 命令行运行: %INSTALL_DIR%\auth_manager.exe
echo.

REM 询问是否立即启动
set /p START_NOW=是否立即启动程序? (y/n): 
if /i "!START_NOW!"=="y" (
    echo 正在启动认证服务管理器...
    start "" "%INSTALL_DIR%\auth_manager.exe"
)

echo.
echo 安装程序结束
pause
