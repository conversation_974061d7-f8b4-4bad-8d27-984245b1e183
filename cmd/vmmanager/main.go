package main

import (
	"flag"
	"fmt"
	"os"
	"os/signal"
	"syscall"

	"license-server-wrapper/internal/config"
	"license-server-wrapper/internal/logger"
	"license-server-wrapper/internal/tray"
	"license-server-wrapper/internal/vmware"
	"license-server-wrapper/pkg/types"
)

var (
	configPath = flag.String("config", "", "配置文件路径")
	logLevel   = flag.String("log", "info", "日志级别 (debug, info, warn, error)")
	version    = flag.Bool("version", false, "显示版本信息")
	help       = flag.Bool("help", false, "显示帮助信息")
)

const (
	AppName    = "认证服务管理器"
	AppVersion = "1.0.0"
	AppDesc    = "VMware认证服务管理工具"
)

func main() {
	flag.Parse()

	if *version {
		fmt.Printf("%s v%s\n%s\n", AppName, AppVersion, AppDesc)
		os.Exit(0)
	}

	if *help {
		showHelp()
		os.Exit(0)
	}

	// 初始化配置管理器
	var configMgr *config.Manager
	if *configPath != "" {
		configMgr = config.NewManagerWithPath(*configPath)
	} else {
		configMgr = config.NewManager()
	}

	// 加载配置
	if err := configMgr.Load(); err != nil {
		fmt.Printf("加载配置失败: %v\n", err)
		os.Exit(1)
	}

	cfg := configMgr.GetConfig()

	// 覆盖日志级别
	if *logLevel != "info" {
		cfg.Log.Level = *logLevel
	}

	// 初始化日志系统
	logger, err := logger.NewLogger(&cfg.Log)
	if err != nil {
		fmt.Printf("初始化日志系统失败: %v\n", err)
		os.Exit(1)
	}
	defer logger.Close()

	logger.WithFields(map[string]interface{}{
		"app":     AppName,
		"version": AppVersion,
		"config":  configMgr.GetConfigPath(),
	}).Info("应用程序启动")

	// 验证配置
	if err := validateConfig(cfg); err != nil {
		logger.WithError(err).Fatal("配置验证失败")
	}

	// 创建认证服务管理器
	vmManager, err := vmware.NewManager(cfg, logger.Logger)
	if err != nil {
		logger.WithError(err).Fatal("创建认证服务管理器失败")
	}

	// 创建系统托盘
	sysTray := tray.NewSysTray(vmManager, cfg, logger.Logger)

	// 设置信号处理
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// 启动系统托盘（阻塞运行）
	go func() {
		logger.Info("启动系统托盘")
		sysTray.Run()
	}()

	// 等待退出信号
	sig := <-sigChan
	logger.WithField("signal", sig.String()).Info("收到退出信号")

	// 优雅关闭
	logger.Info("正在关闭应用程序...")
	sysTray.Stop()
	
	logger.Info("应用程序已退出")
}

// validateConfig 验证配置
func validateConfig(cfg *types.Config) error {
	if cfg.VM.VMXPath == "" {
		return fmt.Errorf("未配置认证服务VMX文件路径")
	}

	if _, err := os.Stat(cfg.VM.VMXPath); os.IsNotExist(err) {
		return fmt.Errorf("认证服务VMX文件不存在: %s", cfg.VM.VMXPath)
	}

	if cfg.VM.Name == "" {
		cfg.VM.Name = "认证服务"
	}

	return nil
}

// showHelp 显示帮助信息
func showHelp() {
	fmt.Printf("%s v%s\n", AppName, AppVersion)
	fmt.Printf("%s\n\n", AppDesc)
	
	fmt.Println("用法:")
	fmt.Printf("  %s [选项]\n\n", os.Args[0])
	
	fmt.Println("选项:")
	fmt.Println("  -config string")
	fmt.Println("        配置文件路径")
	fmt.Println("  -log string")
	fmt.Println("        日志级别 (debug, info, warn, error) (默认 \"info\")")
	fmt.Println("  -version")
	fmt.Println("        显示版本信息")
	fmt.Println("  -help")
	fmt.Println("        显示此帮助信息")
	
	fmt.Println("\n配置文件:")
	fmt.Printf("  默认配置文件位置: %s\n", config.GetDefaultConfigDir())
	fmt.Println("  如果配置文件不存在，将自动创建默认配置")
	
	fmt.Println("\n示例:")
	fmt.Printf("  %s\n", os.Args[0])
	fmt.Printf("  %s -config /path/to/config.yaml\n", os.Args[0])
	fmt.Printf("  %s -log debug\n", os.Args[0])
}
