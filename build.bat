@echo off
setlocal enabledelayedexpansion

REM ========================================
REM 认证服务管理器构建脚本
REM ========================================

echo 认证服务管理器构建脚本
echo ========================

REM 检查Go环境
go version >nul 2>&1
if %errorlevel% neq 0 (
    echo 错误: 未找到Go环境，请先安装Go
    pause
    exit /b 1
)

echo 正在检查Go版本...
for /f "tokens=3" %%i in ('go version') do set GO_VERSION=%%i
echo Go版本: %GO_VERSION%

REM 设置构建参数
set APP_NAME=auth_manager
set VERSION=1.0.0
set BUILD_TIME=%date% %time%
set OUTPUT_DIR=dist

echo.
echo 构建参数:
echo - 应用名称: %APP_NAME%
echo - 版本: %VERSION%
echo - 构建时间: %BUILD_TIME%
echo - 输出目录: %OUTPUT_DIR%
echo.

REM 创建输出目录
if not exist "%OUTPUT_DIR%" mkdir "%OUTPUT_DIR%"

REM 清理之前的构建
if exist "%OUTPUT_DIR%\%APP_NAME%.exe" del "%OUTPUT_DIR%\%APP_NAME%.exe"

echo 正在下载依赖...
go mod tidy
if %errorlevel% neq 0 (
    echo 错误: 下载依赖失败
    pause
    exit /b 1
)

echo 正在构建程序...

REM 设置构建标志
set LDFLAGS=-ldflags "-s -w -X main.version=%VERSION% -X 'main.buildTime=%BUILD_TIME%'"

REM 构建Windows可执行文件
set GOOS=windows
set GOARCH=amd64
go build %LDFLAGS% -o "%OUTPUT_DIR%\%APP_NAME%.exe" cmd/vmmanager/main.go

if %errorlevel% neq 0 (
    echo 错误: 构建失败
    pause
    exit /b 1
)

echo 构建成功！

REM 复制相关文件到输出目录
echo 正在复制相关文件...

REM 复制配置文件
if not exist "%OUTPUT_DIR%\assets" mkdir "%OUTPUT_DIR%\assets"
if not exist "%OUTPUT_DIR%\assets\config" mkdir "%OUTPUT_DIR%\assets\config"
copy "assets\config\default.yaml" "%OUTPUT_DIR%\assets\config\" >nul

REM 复制客户机脚本
if not exist "%OUTPUT_DIR%\scripts" mkdir "%OUTPUT_DIR%\scripts"
if not exist "%OUTPUT_DIR%\scripts\guest" mkdir "%OUTPUT_DIR%\scripts\guest"
copy "scripts\guest\validator.bat" "%OUTPUT_DIR%\scripts\guest\" >nul

REM 复制安装脚本
if not exist "%OUTPUT_DIR%\scripts\install" mkdir "%OUTPUT_DIR%\scripts\install"
copy "scripts\install\setup.bat" "%OUTPUT_DIR%\scripts\install\" >nul

REM 复制文档
if not exist "%OUTPUT_DIR%\docs" mkdir "%OUTPUT_DIR%\docs"
copy "docs\README.md" "%OUTPUT_DIR%\docs\" >nul

REM 创建发布包
echo 正在创建发布包...
set RELEASE_NAME=%APP_NAME%_v%VERSION%_windows_amd64
if exist "%RELEASE_NAME%.zip" del "%RELEASE_NAME%.zip"

REM 使用PowerShell创建ZIP文件
powershell -Command "Compress-Archive -Path '%OUTPUT_DIR%\*' -DestinationPath '%RELEASE_NAME%.zip'"

if %errorlevel% equ 0 (
    echo 发布包创建成功: %RELEASE_NAME%.zip
) else (
    echo 警告: 无法创建发布包
)

REM 显示构建结果
echo.
echo ========================
echo 构建完成！
echo ========================
echo.
echo 输出文件:
dir "%OUTPUT_DIR%\%APP_NAME%.exe"
echo.
echo 文件大小:
for %%i in ("%OUTPUT_DIR%\%APP_NAME%.exe") do echo %%~zi 字节
echo.

REM 询问是否运行测试
set /p RUN_TEST=是否运行程序测试? (y/n): 
if /i "!RUN_TEST!"=="y" (
    echo 正在运行程序测试...
    "%OUTPUT_DIR%\%APP_NAME%.exe" -version
    if %errorlevel% equ 0 (
        echo 程序测试通过
    ) else (
        echo 程序测试失败
    )
)

echo.
echo 构建脚本结束
pause
