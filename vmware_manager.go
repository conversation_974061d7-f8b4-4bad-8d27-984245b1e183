package main

import (
	"fmt"
	"log"
	"os/exec"
	"strings"
	"time"
)

// VMState 虚拟机状态
type VMState int

const (
	VMStateStopped VMState = iota
	VMStateRunning
	VMStateSuspended
	VMStateUnknown
)

func (s VMState) String() string {
	switch s {
	case VMStateStopped:
		return "已停止"
	case VMStateRunning:
		return "运行中"
	case VMStateSuspended:
		return "已暂停"
	default:
		return "未知"
	}
}

// VMwareManager VMware虚拟机管理器
type VMwareManager struct {
	config     *Config
	hardwareID string
	idGen      *IDGenerator
}

// NewVMwareManager 创建新的VMware管理器
func NewVMwareManager(config *Config, hardwareID string) (*VMwareManager, error) {
	if err := config.Validate(); err != nil {
		return nil, fmt.Errorf("配置验证失败: %w", err)
	}

	return &VMwareManager{
		config:     config,
		hardwareID: hardwareID,
		idGen:      NewIDGenerator(),
	}, nil
}

// GetVMState 获取虚拟机状态
func (vm *VMwareManager) GetVMState() (VMState, error) {
	cmd := exec.Command(vm.config.GetVMRunPath(), "list")
	output, err := cmd.Output()
	if err != nil {
		return VMStateUnknown, fmt.Errorf("获取虚拟机列表失败: %w", err)
	}

	outputStr := string(output)
	if strings.Contains(outputStr, vm.config.VMXPath) {
		return VMStateRunning, nil
	}

	// 检查是否暂停
	cmd = exec.Command(vm.config.GetVMRunPath(), "list", "suspended")
	output, err = cmd.Output()
	if err == nil && strings.Contains(string(output), vm.config.VMXPath) {
		return VMStateSuspended, nil
	}

	return VMStateStopped, nil
}

// StartVM 启动虚拟机
func (vm *VMwareManager) StartVM() error {
	log.Println("正在启动认证服务...")

	// 检查当前状态
	state, err := vm.GetVMState()
	if err != nil {
		return fmt.Errorf("检查虚拟机状态失败: %w", err)
	}

	if state == VMStateRunning {
		log.Println("认证服务已在运行中")
		return nil
	}

	// 设置硬件ID到guestVar
	if err := vm.setHardwareIDToGuest(); err != nil {
		return fmt.Errorf("设置硬件ID失败: %w", err)
	}

	// 启动虚拟机
	var cmd *exec.Cmd
	if vm.config.VMPassword != "" {
		cmd = exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "start", vm.config.VMXPath, "nogui")
	} else {
		cmd = exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "start", vm.config.VMXPath, "nogui")
	}

	if err := cmd.Run(); err != nil {
		return fmt.Errorf("启动虚拟机失败: %w", err)
	}

	log.Println("认证服务启动成功")

	// 等待虚拟机完全启动
	time.Sleep(5 * time.Second)

	// 重置硬件ID（安全措施）
	go vm.resetHardwareIDAfterDelay()

	return nil
}

// StopVM 停止虚拟机
func (vm *VMwareManager) StopVM() error {
	log.Println("正在停止认证服务...")

	state, err := vm.GetVMState()
	if err != nil {
		return fmt.Errorf("检查虚拟机状态失败: %w", err)
	}

	if state == VMStateStopped {
		log.Println("认证服务已停止")
		return nil
	}

	// 尝试优雅关机
	cmd := exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "stop", vm.config.VMXPath, "soft")
	if err := cmd.Run(); err != nil {
		log.Printf("优雅关机失败，尝试强制关机: %v", err)
		// 强制关机
		cmd = exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "stop", vm.config.VMXPath, "hard")
		if err := cmd.Run(); err != nil {
			return fmt.Errorf("强制关机失败: %w", err)
		}
	}

	log.Println("认证服务已停止")
	return nil
}

// RestartVM 重启虚拟机
func (vm *VMwareManager) RestartVM() error {
	log.Println("正在重启认证服务...")

	if err := vm.StopVM(); err != nil {
		return fmt.Errorf("停止虚拟机失败: %w", err)
	}

	// 等待完全停止
	time.Sleep(3 * time.Second)

	if err := vm.StartVM(); err != nil {
		return fmt.Errorf("启动虚拟机失败: %w", err)
	}

	log.Println("认证服务重启成功")
	return nil
}

// SuspendVM 暂停虚拟机
func (vm *VMwareManager) SuspendVM() error {
	log.Println("正在暂停认证服务...")

	cmd := exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "suspend", vm.config.VMXPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("暂停虚拟机失败: %w", err)
	}

	log.Println("认证服务已暂停")
	return nil
}

// ResumeVM 恢复虚拟机
func (vm *VMwareManager) ResumeVM() error {
	log.Println("正在恢复认证服务...")

	cmd := exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "unpause", vm.config.VMXPath)
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("恢复虚拟机失败: %w", err)
	}

	log.Println("认证服务已恢复")
	return nil
}

// setHardwareIDToGuest 设置硬件ID到客户机
func (vm *VMwareManager) setHardwareIDToGuest() error {
	log.Println("正在设置硬件ID到客户机...")

	// 加密硬件ID
	encryptedID, err := Encrypt(vm.hardwareID)
	if err != nil {
		return fmt.Errorf("加密硬件ID失败: %w", err)
	}

	// 设置guestVar
	cmd := exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "writeVariable", 
		vm.config.VMXPath, "guestVar", "hardware_id", encryptedID)
	
	if err := cmd.Run(); err != nil {
		return fmt.Errorf("设置guestVar失败: %w", err)
	}

	log.Println("硬件ID已设置到客户机")
	return nil
}

// resetHardwareIDAfterDelay 延迟重置硬件ID
func (vm *VMwareManager) resetHardwareIDAfterDelay() {
	// 等待客户机启动并验证完成
	time.Sleep(60 * time.Second)

	log.Println("正在重置硬件ID...")

	// 生成新的重置ID
	resetID := vm.idGen.GenerateResetID(vm.hardwareID)
	encryptedResetID, err := Encrypt(resetID)
	if err != nil {
		log.Printf("加密重置ID失败: %v", err)
		return
	}

	// 更新guestVar
	cmd := exec.Command(vm.config.GetVMRunPath(), "-T", "ws", "writeVariable", 
		vm.config.VMXPath, "guestVar", "hardware_id", encryptedResetID)
	
	if err := cmd.Run(); err != nil {
		log.Printf("重置硬件ID失败: %v", err)
		return
	}

	log.Println("硬件ID已重置")
}

// GetVMInfo 获取虚拟机信息
func (vm *VMwareManager) GetVMInfo() (map[string]string, error) {
	info := make(map[string]string)

	state, err := vm.GetVMState()
	if err != nil {
		return nil, err
	}

	info["状态"] = state.String()
	info["虚拟机文件"] = vm.config.VMXPath
	info["硬件ID"] = vm.hardwareID[:8] + "..."

	return info, nil
}
